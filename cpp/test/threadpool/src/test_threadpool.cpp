#include <gtest/gtest.h>
#include <zexuan/thread_pool.hpp>
#include <chrono>
#include <atomic>
#include <vector>
#include <future>
#include <thread>
using namespace zexuan;
class ThreadPoolTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 测试前的设置
    }

    void TearDown() override {
        // 测试后的清理
    }
};

// 测试线程池基本创建和销毁
TEST_F(ThreadPoolTest, BasicCreationAndDestruction) {
    // 创建线程池
    {
        ThreadPool pool(4);
        // 线程池应该正常创建
        SUCCEED();
    }
    // 线程池应该正常销毁
    SUCCEED();
}

// 测试单个任务提交和执行
TEST_F(ThreadPoolTest, SingleTaskExecution) {
    ThreadPool pool(2);
    
    std::atomic<int> counter{0};
    
    // 提交一个简单任务
    auto future = pool.commit([&counter]() {
        counter++;
        return 42;
    });
    
    // 等待任务完成
    int result = future.get();
    
    EXPECT_EQ(result, 42);
    EXPECT_EQ(counter.load(), 1);
}

// 测试多个任务并发执行
TEST_F(ThreadPoolTest, MultipleTasksExecution) {
    ThreadPool pool(4);
    
    std::atomic<int> counter{0};
    std::vector<std::future<int>> futures;
    
    // 提交多个任务
    for (int i = 0; i < 10; ++i) {
        futures.push_back(pool.commit([&counter, i]() {
            counter++;
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return i * 2;
        }));
    }
    
    // 等待所有任务完成并验证结果
    for (int i = 0; i < 10; ++i) {
        int result = futures[i].get();
        EXPECT_EQ(result, i * 2);
    }
    
    EXPECT_EQ(counter.load(), 10);
}

// 测试带参数的任务
TEST_F(ThreadPoolTest, TaskWithParameters) {
    ThreadPool pool(2);
    
    // 测试带多个参数的函数
    auto add = [](int a, int b, int c) {
        return a + b + c;
    };
    
    auto future = pool.commit(add, 1, 2, 3);
    int result = future.get();
    
    EXPECT_EQ(result, 6);
}

// 测试任务执行顺序（不保证顺序，但都应该执行）
TEST_F(ThreadPoolTest, TaskExecutionOrder) {
    ThreadPool pool(1); // 单线程确保顺序
    
    std::vector<int> results;
    std::mutex results_mutex;
    std::vector<std::future<void>> futures;
    
    // 提交有序任务
    for (int i = 0; i < 5; ++i) {
        futures.push_back(pool.commit([&results, &results_mutex, i]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            std::lock_guard<std::mutex> lock(results_mutex);
            results.push_back(i);
        }));
    }
    
    // 等待所有任务完成
    for (auto& future : futures) {
        future.get();
    }
    
    // 验证所有任务都执行了
    EXPECT_EQ(results.size(), 5);
    
    // 由于是单线程池，应该按提交顺序执行
    for (int i = 0; i < 5; ++i) {
        EXPECT_EQ(results[i], i);
    }
}

// 测试异常处理
TEST_F(ThreadPoolTest, ExceptionHandling) {
    ThreadPool pool(2);
    
    // 提交一个会抛出异常的任务
    auto future = pool.commit([]() -> int {
        throw std::runtime_error("Test exception");
        return 42;
    });
    
    // 应该能捕获异常
    EXPECT_THROW(future.get(), std::runtime_error);
}

// 测试返回不同类型的任务
TEST_F(ThreadPoolTest, DifferentReturnTypes) {
    ThreadPool pool(2);
    
    // 返回 string
    auto string_future = pool.commit([]() {
        return std::string("Hello World");
    });
    
    // 返回 double
    auto double_future = pool.commit([]() {
        return 3.14159;
    });
    
    // 返回 void
    std::atomic<bool> void_task_executed{false};
    auto void_future = pool.commit([&void_task_executed]() {
        void_task_executed = true;
    });
    
    EXPECT_EQ(string_future.get(), "Hello World");
    EXPECT_DOUBLE_EQ(double_future.get(), 3.14159);
    void_future.get();
    EXPECT_TRUE(void_task_executed.load());
}

// 测试大量任务的性能
TEST_F(ThreadPoolTest, HighLoadPerformance) {
    ThreadPool pool(std::thread::hardware_concurrency());
    
    const int task_count = 1000;
    std::atomic<int> completed_tasks{0};
    std::vector<std::future<void>> futures;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 提交大量轻量级任务
    for (int i = 0; i < task_count; ++i) {
        futures.push_back(pool.commit([&completed_tasks]() {
            // 模拟一些轻量级工作
            volatile int sum = 0;
            for (int j = 0; j < 100; ++j) {
                sum += j;
            }
            completed_tasks++;
        }));
    }
    
    // 等待所有任务完成
    for (auto& future : futures) {
        future.get();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(completed_tasks.load(), task_count);
    
    // 性能测试：1000个任务应该在合理时间内完成（比如5秒内）
    EXPECT_LT(duration.count(), 5000);
    
    std::cout << "Completed " << task_count << " tasks in " << duration.count() << "ms" << std::endl;
}

// 测试线程池在销毁时等待任务完成
TEST_F(ThreadPoolTest, DestructorWaitsForTasks) {
    std::atomic<int> completed_tasks{0};
    
    {
        ThreadPool pool(2);
        
        // 提交一些需要时间的任务
        for (int i = 0; i < 4; ++i) {
            pool.commit([&completed_tasks]() {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                completed_tasks++;
                return 0;
            });
        }
        
        // 线程池销毁时应该等待所有任务完成
    }
    
    // 所有任务都应该完成
    EXPECT_EQ(completed_tasks.load(), 4);
}

// 测试零线程池（边界情况）
TEST_F(ThreadPoolTest, ZeroThreadPool) {
    // 创建零线程的线程池应该仍然能工作（虽然不实用）
    ThreadPool pool(0);

    // 提交任务应该不会崩溃，但可能不会执行
    auto future = pool.commit([]() {
        return 42;
    });

    // 这个测试主要确保不会崩溃
    SUCCEED();
}

// 测试单线程池
TEST_F(ThreadPoolTest, SingleThreadPool) {
    ThreadPool pool(1);

    std::vector<int> execution_order;
    std::mutex order_mutex;
    std::vector<std::future<void>> futures;

    // 提交多个任务到单线程池
    for (int i = 0; i < 5; ++i) {
        futures.push_back(pool.commit([&execution_order, &order_mutex, i]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            std::lock_guard<std::mutex> lock(order_mutex);
            execution_order.push_back(i);
        }));
    }

    // 等待所有任务完成
    for (auto& future : futures) {
        future.get();
    }

    // 单线程池应该按顺序执行
    EXPECT_EQ(execution_order.size(), 5);
    for (int i = 0; i < 5; ++i) {
        EXPECT_EQ(execution_order[i], i);
    }
}

// 测试线程安全性
TEST_F(ThreadPoolTest, ThreadSafety) {
    ThreadPool pool(4);

    std::atomic<int> shared_counter{0};
    std::vector<std::future<void>> futures;

    // 多个线程同时修改共享变量
    for (int i = 0; i < 100; ++i) {
        futures.push_back(pool.commit([&shared_counter]() {
            for (int j = 0; j < 100; ++j) {
                shared_counter++;
            }
        }));
    }

    // 等待所有任务完成
    for (auto& future : futures) {
        future.get();
    }

    // 验证原子操作的正确性
    EXPECT_EQ(shared_counter.load(), 10000);
}

// 测试任务队列的FIFO特性（在单线程情况下）
TEST_F(ThreadPoolTest, TaskQueueFIFO) {
    ThreadPool pool(1); // 单线程确保FIFO

    std::vector<int> results;
    std::mutex results_mutex;
    std::vector<std::future<void>> futures;

    // 快速提交多个任务
    for (int i = 0; i < 10; ++i) {
        futures.push_back(pool.commit([&results, &results_mutex, i]() {
            // 不添加延迟，测试队列顺序
            std::lock_guard<std::mutex> lock(results_mutex);
            results.push_back(i);
        }));
    }

    // 等待所有任务完成
    for (auto& future : futures) {
        future.get();
    }

    // 验证FIFO顺序
    EXPECT_EQ(results.size(), 10);
    for (int i = 0; i < 10; ++i) {
        EXPECT_EQ(results[i], i);
    }
}

// 测试复杂对象作为返回值
TEST_F(ThreadPoolTest, ComplexReturnTypes) {
    ThreadPool pool(2);

    // 返回vector
    auto vector_future = pool.commit([]() {
        std::vector<int> vec = {1, 2, 3, 4, 5};
        return vec;
    });

    // 返回pair
    auto pair_future = pool.commit([]() {
        return std::make_pair(std::string("key"), 42);
    });

    auto vec_result = vector_future.get();
    auto pair_result = pair_future.get();

    EXPECT_EQ(vec_result.size(), 5);
    EXPECT_EQ(vec_result[0], 1);
    EXPECT_EQ(vec_result[4], 5);

    EXPECT_EQ(pair_result.first, "key");
    EXPECT_EQ(pair_result.second, 42);
}

// 测试lambda捕获
TEST_F(ThreadPoolTest, LambdaCapture) {
    ThreadPool pool(2);

    int captured_value = 100;
    std::string captured_string = "test";

    // 按值捕获
    auto future1 = pool.commit([captured_value, captured_string]() {
        return captured_value + static_cast<int>(captured_string.length());
    });

    // 按引用捕获（需要小心生命周期）
    std::atomic<int> ref_value{200};
    auto future2 = pool.commit([&ref_value]() {
        return ref_value.load() * 2;
    });

    EXPECT_EQ(future1.get(), 104); // 100 + 4
    EXPECT_EQ(future2.get(), 400); // 200 * 2
}
