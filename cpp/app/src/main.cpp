/**
 * @file main.cpp
 * @brief Automated plugin system with SystemInitializer
 * <AUTHOR> project
 * @date 2024
 */

#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include <iostream>
#include <filesystem>

using namespace zexuan;

/**
 * @brief 测试插件系统的文件重命名功能
 * @param directoryPath 要处理的目录路径
 */
void testFileRenaming(const std::string& directoryPath) {
    std::cout << "=== Testing File Renaming with Automated Plugin System ===" << std::endl;

    try {
        // 获取已加载的插件
        const auto& plugins = SystemInitializer::getLoadedPlugins();
        if (plugins.empty()) {
            std::cerr << "No plugins loaded! Make sure SystemInitializer has been called." << std::endl;
            return;
        }

        std::cout << "Found " << plugins.size() << " loaded plugins:" << std::endl;
        for (const auto& plugin : plugins) {
            if (plugin) {
                std::cout << "  - Plugin ID " << plugin->getPluginId()
                          << ": " << plugin->getPluginDescription() << std::endl;
            }
        }

        // 查找 comic_plugin (ID = 1)
        auto comicPlugin = SystemInitializer::getPlugin(1);
        if (!comicPlugin) {
            std::cerr << "Comic plugin (ID=1) not found!" << std::endl;
            return;
        }

        std::cout << "\nUsing plugin: " << comicPlugin->getPluginDescription() << std::endl;

        // 获取共享的 mediator 实例
        auto mediator = SingletonRegistry::getInstance().get<base::BaseMediator>();

        // 创建文件重命名消息
        base::Message renameMessage(
            0x01,  // TYP: File operation message type
            0x81,  // VSQ: Single information object
            0x06,  // COT: Activation
            0,     // Source: Main application
            0x01,  // FUN: File rename operation
            0x00   // INF: Information number
        );
        renameMessage.setTarget(2);  // Target: Comic Plugin (ID=1)

        // 将目录路径存储到消息的 variableStructure_ 中
        renameMessage.setTextContent(directoryPath);

        std::cout << "\nSending file rename request for directory: " << directoryPath << std::endl;

        // 发送消息给插件
        std::string description;
        int result = mediator->sendMessage(renameMessage, description);

        if (result == 0) {
            std::cout << "✓ Message sent successfully!" << std::endl;
            if (!description.empty()) {
                std::cout << "  Description: " << description << std::endl;
            }
        } else {
            std::cerr << "✗ Failed to send message, error code: " << result << std::endl;
            if (!description.empty()) {
                std::cerr << "  Error description: " << description << std::endl;
            }
        }

        // 显示 mediator 统计信息
        std::cout << "\nMediator statistics:" << std::endl;
        std::cout << "  Registered observers: " << mediator->getObserverCount() << std::endl;

        std::cout << "\n=== File Renaming Test Completed ===" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Exception occurred during file renaming test: " << e.what() << std::endl;
    }
}

int main(int argc, char* argv[]) {
    std::cout << "=== Zexuan Automated Plugin System ===" << std::endl;

    try {
        // 1. 使用 SystemManager 进行 RAII 风格的系统管理
        SystemManager systemManager;
        std::cout << "✓ System initialized successfully" << std::endl;

        // 2. 解析命令行参数
        std::string directoryPath;
        if (argc >= 2) {
            directoryPath = argv[1];

            // 检查目录是否存在
            if (!std::filesystem::exists(directoryPath)) {
                std::cerr << "Error: Directory '" << directoryPath << "' does not exist." << std::endl;
                return 1;
            }

            if (!std::filesystem::is_directory(directoryPath)) {
                std::cerr << "Error: '" << directoryPath << "' is not a directory." << std::endl;
                return 1;
            }

            std::cout << "✓ Target directory: " << directoryPath << std::endl;

            // 3. 执行文件重命名测试
            testFileRenaming(directoryPath);

        } else {
            std::cout << "No directory specified,return 1" << std::endl;
            return 1;
        }

        std::cout << "\n🎉 Program completed successfully!" << std::endl;
        std::cout << "System will automatically cleanup when exiting..." << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ Program failed: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Program failed: Unknown exception" << std::endl;
        return 1;
    }

    return 0;
}