#include "system_initializer.hpp"
#include <stdexcept>
#include <nlohmann/json.hpp>

namespace zexuan {

// 静态成员变量定义
std::vector<std::shared_ptr<plugin::PluginBase>> SystemInitializer::loadedPlugins_;
bool SystemInitializer::initialized_ = false;

void SystemInitializer::initialize(const std::string& configPath) {
    if (initialized_) {
        std::cout << "SystemInitializer: Already initialized" << std::endl;
        return;
    }

    std::cout << "SystemInitializer: Starting simplified system initialization..." << std::endl;

    auto& registry = SingletonRegistry::getInstance();

    try {
        // 1. 只注册 BaseMediator 单例（插件需要共享同一个 mediator 实例）
        std::cout << "SystemInitializer: Registering BaseMediator singleton..." << std::endl;
        auto mediator = registry.get<base::BaseMediator>();
        if (!mediator->initialize()) {
            throw std::runtime_error("Failed to initialize BaseMediator");
        }
        std::cout << "SystemInitializer: BaseMediator registered and initialized" << std::endl;

        // 2. 加载插件
        std::cout << "SystemInitializer: Loading plugins from config..." << std::endl;
        loadPluginsFromConfig(configPath);

        // 3. 初始化所有插件
        initializeAllPlugins();

        initialized_ = true;
        std::cout << "SystemInitializer: System initialization completed successfully!" << std::endl;
        std::cout << "SystemInitializer: Loaded " << loadedPlugins_.size() << " plugins" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "SystemInitializer: Initialization failed: " << e.what() << std::endl;
        cleanup();  // 清理已创建的资源
        throw;
    }
}

void SystemInitializer::cleanup() {
    if (!initialized_) {
        return;
    }

    std::cout << "SystemInitializer: Starting system cleanup..." << std::endl;

    try {
        // 1. 关闭所有插件
        shutdownAllPlugins();

        // 2. 清理插件列表
        loadedPlugins_.clear();

        // 3. DynamicLibraryLoader 会在其析构函数中自动卸载动态库
        // BaseMediator 会在 SingletonRegistry 析构时自动清理

        initialized_ = false;
        std::cout << "SystemInitializer: System cleanup completed" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "SystemInitializer: Cleanup error: " << e.what() << std::endl;
    }
}

const std::vector<std::shared_ptr<plugin::PluginBase>>& SystemInitializer::getLoadedPlugins() {
    return loadedPlugins_;
}

std::shared_ptr<plugin::PluginBase> SystemInitializer::getPlugin(int pluginId) {
    for (const auto& plugin : loadedPlugins_) {
        if (plugin && plugin->getPluginId() == pluginId) {
            return plugin;
        }
    }
    return nullptr;
}

void SystemInitializer::loadPluginsFromConfig(const std::string& configPath) {
    // 创建 ConfigLoader 实例
    ConfigLoader config;
    config.loadFromFile(configPath);

    std::cout << "SystemInitializer: Loading plugins from configuration: " << configPath << std::endl;

    // 获取插件配置数组
    auto pluginsJson = config.get<nlohmann::json>("plugins", nlohmann::json::array());

    if (pluginsJson.empty()) {
        throw std::runtime_error("No plugins found in configuration file: " + configPath);
    }

    std::cout << "SystemInitializer: Found " << pluginsJson.size() << " plugins to load" << std::endl;

    // 遍历插件配置
    for (const auto& pluginConfig : pluginsJson) {
        try {
            // 检查插件是否启用
            bool enabled = pluginConfig.value("enabled", true);
            if (!enabled) {
                std::cout << "SystemInitializer: Skipping disabled plugin" << std::endl;
                continue;
            }

            // 获取插件配置
            int pluginId = pluginConfig.value("id", 0);
            std::string description = pluginConfig.value("description", "");
            std::string libraryPath = pluginConfig.value("library_path", "");

            // 验证必需字段
            if (pluginId == 0) {
                std::cerr << "SystemInitializer: ✗ Plugin configuration missing id" << std::endl;
                continue;
            }
            if (description.empty()) {
                std::cerr << "SystemInitializer: ✗ Plugin configuration missing description" << std::endl;
                continue;
            }
            if (libraryPath.empty()) {
                std::cerr << "SystemInitializer: ✗ Plugin configuration missing library_path" << std::endl;
                continue;
            }

            std::cout << "SystemInitializer: Loading plugin " << pluginId
                      << " (" << description << ") from " << libraryPath << std::endl;

            auto plugin = loadPlugin(libraryPath, pluginId, description);
            if (plugin) {
                loadedPlugins_.push_back(plugin);
                std::cout << "SystemInitializer: ✓ Successfully loaded plugin " << pluginId
                          << " (" << description << ") from " << libraryPath << std::endl;
            }

        } catch (const std::exception& e) {
            std::cerr << "SystemInitializer: ✗ Failed to load plugin: " << e.what() << std::endl;
            // 插件加载失败时继续加载其他插件，但会记录错误
        }
    }

    if (loadedPlugins_.empty()) {
        throw std::runtime_error("Failed to load any plugins from configuration");
    }
}

std::shared_ptr<plugin::PluginBase> SystemInitializer::loadPlugin(const std::string& libraryPath, int pluginId, const std::string& description) {
    // 使用 DynamicLibraryLoader 加载动态库
    static base::DynamicLibraryLoader loader;  // 静态实例，确保库在程序结束前不被卸载

    if (!loader.loadLibrary(libraryPath)) {
        throw std::runtime_error("Cannot load library: " + loader.getLastError());
    }

    // 获取插件创建函数
    typedef plugin::PluginBase* (*create_plugin_func)(void*, int, const char*);
    auto create_plugin = loader.getFunction<create_plugin_func>(libraryPath, "create_plugin");

    if (!create_plugin) {
        throw std::runtime_error("Cannot load symbol 'create_plugin': " + loader.getLastError());
    }

    // 获取共享的 mediator 实例
    auto mediator = SingletonRegistry::getInstance().get<base::BaseMediator>();

    // 创建插件实例 - 传递指向智能指针的指针
    plugin::PluginBase* plugin_ptr = create_plugin(&mediator, pluginId, description.c_str());
    if (!plugin_ptr) {
        throw std::runtime_error("Failed to create plugin instance");
    }

    // 获取销毁函数（可选）
    typedef void (*destroy_plugin_func)(plugin::PluginBase*);
    auto destroy_plugin = loader.getFunction<destroy_plugin_func>(libraryPath, "destroy_plugin");

    // 返回智能指针包装的插件实例
    return std::shared_ptr<plugin::PluginBase>(plugin_ptr, [destroy_plugin](plugin::PluginBase* p) {
        // 自定义删除器：使用插件的销毁函数或普通删除
        if (p) {
            if (destroy_plugin) {
                destroy_plugin(p);
            } else {
                delete p;  // 回退到普通删除
            }
        }
    });
}

void SystemInitializer::initializeAllPlugins() {
    std::cout << "SystemInitializer: Initializing all plugins..." << std::endl;

    for (auto& plugin : loadedPlugins_) {
        if (plugin) {
            try {
                if (plugin->initialize()) {
                    std::cout << "SystemInitializer: Plugin " << plugin->getPluginId()
                              << " initialized successfully" << std::endl;
                } else {
                    std::cerr << "SystemInitializer: Plugin " << plugin->getPluginId()
                              << " initialization failed" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cerr << "SystemInitializer: Plugin " << plugin->getPluginId()
                          << " initialization error: " << e.what() << std::endl;
            }
        }
    }
}

void SystemInitializer::shutdownAllPlugins() {
    std::cout << "SystemInitializer: Shutting down all plugins..." << std::endl;

    for (auto& plugin : loadedPlugins_) {
        if (plugin) {
            try {
                plugin->shutdown();
                std::cout << "SystemInitializer: Plugin " << plugin->getPluginId()
                          << " shutdown successfully" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "SystemInitializer: Plugin " << plugin->getPluginId()
                          << " shutdown error: " << e.what() << std::endl;
            }
        }
    }
}

} // namespace zexuan
